import { NextRequest, NextResponse } from 'next/server'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Test endpoint to verify Mem0 connection and functionality
 */
export async function GET() {
  try {
    console.log('🧪 Testing Mem0 connection...')
    
    // Test 1: Check if service is available
    const isAvailable = memoryService.isAvailable()
    console.log(`📊 Memory service available: ${isAvailable}`)
    
    // Test 2: Initialize service
    await memoryService.initialize()
    
    // Test 3: Test adding a simple memory
    const testDocumentId = `test-doc-${Date.now()}`
    const testUserId = `test-user-${Date.now()}`

    console.log('🧪 Testing memory addition...')
    await memoryService.addConversationPair(
      testDocumentId,
      testUserId,
      'Hello, this is a test message',
      'Hello! This is a test response from the AI tutor.'
    )
    
    // Test 4: Test memory retrieval
    console.log('🧪 Testing memory retrieval...')
    const memories = await memoryService.getContextualMemory(
      testSessionId,
      'test message'
    )
    
    // Test 5: Get all users to verify connection
    console.log('🧪 Testing users endpoint...')
    const allMemories = await memoryService.getAllMemories(testSessionId)
    
    return NextResponse.json({
      success: true,
      tests: {
        serviceAvailable: isAvailable,
        memoryAddition: 'completed',
        memoryRetrieval: {
          found: memories.length,
          memories: memories
        },
        allMemories: {
          count: allMemories.length,
          memories: allMemories
        }
      },
      message: 'Mem0 connection test completed successfully',
      testSessionId
    })
    
  } catch (error) {
    console.error('❌ Mem0 test failed:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }, { status: 500 })
  }
}

/**
 * POST endpoint to test specific memory operations
 */
export async function POST(request: NextRequest) {
  try {
    const { action, sessionId, message, query } = await request.json()
    
    switch (action) {
      case 'add':
        await memoryService.addConversationMessage(sessionId, message, 'user')
        return NextResponse.json({ success: true, action: 'add', sessionId })
        
      case 'search':
        const results = await memoryService.getContextualMemory(sessionId, query)
        return NextResponse.json({ success: true, action: 'search', results })
        
      case 'init':
        const newSessionId = await memoryService.initializeSession('test-user', 'test-doc')
        return NextResponse.json({ success: true, action: 'init', sessionId: newSessionId })
        
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
    
  } catch (error) {
    console.error('❌ Mem0 POST test failed:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
