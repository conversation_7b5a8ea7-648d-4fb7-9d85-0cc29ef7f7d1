import { MemoryClient } from 'mem0ai'
import { dbService } from '@/lib/db-service'
import { AIErrorHandler, ErrorType } from './error-handler'

export interface ConversationContext {
    userId: string
    documentId: string
    chunkIndex: number
    chunkContent: string
    memoryContext: string[]
}

export enum BloomLevel {
    REMEMBER = 'remember',
    UNDERSTAND = 'understand',
    APPLY = 'apply',
    ANALYZE = 'analyze',
    EVALUATE = 'evaluate',
    CREATE = 'create'
}

export class MemoryService {
    private client: MemoryClient | null = null
    private initialized = false
    private available = false

    constructor() {
        if (!process.env.MEM0_API_KEY) {
            console.warn('⚠️ MEM0_API_KEY not found - memory service will run in degraded mode')
            this.available = false
            return
        }

        try {
            this.client = new MemoryClient({
                apiKey: process.env.MEM0_API_KEY
            })
            this.available = true
            console.log('✅ Mem0 client initialized with async mode support')
        } catch (error) {
            console.error('❌ Failed to initialize Mem0 client:', error)
            this.available = false
        }
    }

    async initialize(): Promise<void> {
        if (this.initialized) return

        if (!this.available || !this.client) {
            console.log('⚠️ Mem0 service running in degraded mode - memory features disabled')
            this.initialized = true
            return
        }

        try {
            // Test the connection with async-compatible method
            console.log('🔍 Testing Mem0 connection...')
            await this.client.users()
            this.initialized = true
            console.log('✅ Mem0 service initialized successfully with async support')
        } catch (error) {
            console.error('❌ Failed to initialize Mem0 service:', error)
            console.error('Error details:', error)
            this.available = false
            this.initialized = true
            console.log('⚠️ Mem0 service switched to degraded mode')
        }
    }

    /**
     * Check if memory service is available
     */
    isAvailable(): boolean {
        return this.available && this.client !== null
    }

    /**
     * Execute memory operation with graceful degradation
     */
    private async executeMemoryOperation<T>(
        operation: () => Promise<T>,
        fallbackValue: T,
        operationName: string
    ): Promise<T> {
        if (!this.isAvailable()) {
            console.log(`⚠️ Memory service unavailable - ${operationName} skipped`)
            return fallbackValue
        }

        try {
            return await AIErrorHandler.withRetry(operation, 'mem0', 2)
        } catch (error) {
            const aiError = AIErrorHandler.classifyError(error, 'mem0')
            AIErrorHandler.logError(aiError, operationName)

            // Mark service as unavailable for certain errors
            if (aiError.type === ErrorType.AUTHENTICATION_ERROR ||
                aiError.type === ErrorType.NETWORK_ERROR) {
                this.available = false
                console.log('⚠️ Mem0 service marked as unavailable due to persistent errors')
            }

            return fallbackValue
        }
    }

    async initializeSession(userId: string, documentId: string): Promise<string> {
        await this.initialize()

        // Create unique session ID with timestamp and random component
        const timestamp = Date.now()
        const randomSuffix = Math.random().toString(36).substring(2, 8)
        const sessionId = `${userId}-${documentId}-${timestamp}-${randomSuffix}`

        // Always update Progress table with sessionId
        await dbService.progress.upsert({
            where: {
                userId_documentId: {
                    userId,
                    documentId
                }
            },
            update: { sessionId },
            create: {
                userId,
                documentId,
                sessionId,
                currentChunk: 0
            }
        })

        // Initialize memory session with proper async mode
        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')

                console.log(`🚀 Initializing Mem0 session for user: ${userId}, document: ${documentId}`)

                const result = await this.client.add([{
                    role: 'assistant',
                    content: `Starting new tutoring session for document. User: ${userId}, Session: ${sessionId}`
                }], {
                    user_id: userId,
                    agent_id: 'guided-tutor',
                    run_id: sessionId,
                    async_mode: true,  // Enable async processing
                    metadata: {
                        session_type: 'tutoring',
                        document_id: documentId,
                        user_id: userId,
                        session_id: sessionId,
                        created_at: new Date().toISOString(),
                        type: 'session_start'
                    }
                })

                console.log(`✅ Session initialized in Mem0 (async):`, result)
                return true
            },
            false,
            'session initialization'
        )

        console.log(`✅ Initialized session: ${sessionId} (memory: ${this.isAvailable() ? 'enabled' : 'disabled'})`)
        return sessionId
    }

    async addLearningMemory(
        sessionId: string,
        content: string,
        category: string,
        bloomLevel: BloomLevel
    ): Promise<void> {
        await this.initialize()

        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                await this.client.add([{
                    role: 'assistant',
                    content
                }], {
                    user_id: sessionId,
                    metadata: {
                        category,
                        bloom_level: bloomLevel,
                        timestamp: new Date().toISOString(),
                        type: 'learning_progress'
                    }
                })
                return true
            },
            false,
            'learning memory addition'
        )
    }

    async addConceptUnderstanding(
        sessionId: string,
        concept: string,
        understandingLevel: number,
        evidence: string
    ): Promise<void> {
        await this.initialize()

        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                const content = `Concept: ${concept} | Understanding Level: ${understandingLevel}/5 | Evidence: ${evidence}`

                await this.client.add([{
                    role: 'assistant',
                    content
                }], {
                    user_id: sessionId,
                    metadata: {
                        category: 'concept_understanding',
                        concept,
                        understanding_level: understandingLevel,
                        evidence,
                        type: 'assessment'
                    }
                })
                return true
            },
            false,
            'concept understanding addition'
        )
    }

    async addMisconception(
        sessionId: string,
        misconception: string,
        correction: string
    ): Promise<void> {
        await this.initialize()

        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                const content = `Misconception: ${misconception} | Correction: ${correction}`

                await this.client.add([{
                    role: 'assistant',
                    content
                }], {
                    user_id: sessionId,
                    metadata: {
                        category: 'misconception',
                        misconception,
                        correction,
                        needs_reinforcement: true,
                        type: 'error_correction'
                    }
                })
                return true
            },
            false,
            'misconception addition'
        )
    }

    async addConversationMessage(
        documentId: string,
        userId: string,
        message: string,
        role: 'user' | 'assistant',
        chunkIndex?: number,
        isInitialMessage: boolean = false
    ): Promise<void> {
        await this.initialize()

        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')

                // Use synchronous mode for initial messages to ensure immediate availability
                const asyncMode = !isInitialMessage
                const modeText = asyncMode ? 'async' : 'sync'

                console.log(`💾 Adding ${role} message to Mem0 (${modeText} mode) for user: ${userId}, document: ${documentId}`)

                const result = await this.client.add([{
                    role: role,
                    content: message
                }], {
                    user_id: userId,
                    agent_id: 'guided-tutor',
                    run_id: documentId,  // Use documentId for document-specific memory
                    async_mode: asyncMode,
                    metadata: {
                        category: 'conversation',
                        role,
                        timestamp: new Date().toISOString(),
                        type: 'chat_message',
                        document_id: documentId,
                        chunk_index: chunkIndex,
                        conversation_type: 'document_learning',
                        is_initial: isInitialMessage
                    }
                })

                if (asyncMode) {
                    console.log(`✅ Successfully queued ${role} message to Mem0 (async):`, result)
                    console.log(`📊 Memory will appear in dashboard within a few seconds`)
                } else {
                    console.log(`✅ Successfully stored ${role} message to Mem0 (sync):`, result)
                }
                return true
            },
            false,
            'conversation message addition'
        )
    }

    /**
     * Add conversation pair (user message + AI response) in one call
     * More efficient than separate calls
     */
    async addConversationPair(
        sessionId: string,
        userMessage: string,
        assistantMessage: string
    ): Promise<void> {
        await this.initialize()

        // Extract user ID from session ID (format: userId-documentId-timestamp)
        const userId = sessionId.split('-')[0]

        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')

                console.log(`💾 Adding conversation pair to Mem0 (async mode) for user: ${userId}`)

                const result = await this.client.add([
                    {
                        role: 'user',
                        content: userMessage
                    },
                    {
                        role: 'assistant',
                        content: assistantMessage
                    }
                ], {
                    user_id: userId,
                    agent_id: 'guided-tutor',
                    run_id: sessionId,
                    async_mode: true,  // Enable async processing
                    metadata: {
                        category: 'conversation',
                        timestamp: new Date().toISOString(),
                        type: 'conversation_pair',
                        session_id: sessionId
                    }
                })

                console.log(`✅ Successfully queued conversation pair to Mem0 (async):`, result)
                console.log(`📊 Memories will appear in dashboard within a few seconds`)
                return true
            },
            false,
            'conversation pair addition'
        )
    }

    async getContextualMemory(
        sessionId: string,
        query: string,
        _bloomLevel?: BloomLevel
    ): Promise<any[]> {
        await this.initialize()

        // Extract user ID from session ID (format: userId-documentId-timestamp)
        const userId = sessionId.split('-')[0]

        return await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')

                console.log(`🔍 Searching memories for user: ${userId}, query: "${query.substring(0, 50)}..."`)

                // Try multiple search approaches for better results
                let memories: any[] = []
                
                // Approach 1: Search by user_id with v2 API
                try {
                    const searchOptions: any = {
                        user_id: userId,
                        limit: 5
                    }

                    memories = await this.client.search(query, searchOptions)
                    console.log(`🔍 User search found ${memories?.length || 0} memories`)
                } catch (error) {
                    console.warn('User search failed, trying agent search:', error)
                }

                // Approach 2: If no results, try searching by agent_id
                if (!memories || memories.length === 0) {
                    try {
                        const agentSearchOptions: any = {
                            agent_id: 'guided-tutor',
                            limit: 5
                        }

                        memories = await this.client.search(query, agentSearchOptions)
                        console.log(`🤖 Agent search found ${memories?.length || 0} memories`)
                    } catch (error) {
                        console.warn('Agent search also failed:', error)
                    }
                }

                // Approach 3: If still no results, try getting all memories for the session
                if (!memories || memories.length === 0) {
                    try {
                        const allMemories = await this.client.getAll({ user_id: userId })
                        memories = allMemories?.slice(0, 5) || []
                        console.log(`📚 Fallback: Retrieved ${memories.length} recent memories`)
                    } catch (error) {
                        console.warn('Fallback memory retrieval failed:', error)
                    }
                }

                console.log(`📚 Found ${memories?.length || 0} relevant memories`)
                return memories || []
            },
            [],
            'contextual memory retrieval'
        )
    }

    async getConceptProgress(sessionId: string, concept: string): Promise<any[]> {
        await this.initialize()

        return await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                // Extract user ID from session ID (format: userId-documentId-timestamp)
                const userId = sessionId.split('-')[0]
                return await this.client.search(concept, { user_id: userId }) || []
            },
            [],
            'concept progress retrieval'
        )
    }

    async getMisconceptions(sessionId: string): Promise<any[]> {
        await this.initialize()

        return await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                // Extract user ID from session ID (format: userId-documentId-timestamp)
                const userId = sessionId.split('-')[0]
                return await this.client.search('misconception correction', { user_id: userId }) || []
            },
            [],
            'misconceptions retrieval'
        )
    }

    async buildEducationalContext(
        sessionId: string,
        currentQuery: string,
        chunkContent: string
    ): Promise<string> {
        await this.initialize()

        try {
            // Get contextually relevant memories
            const relevantMemories = await this.getContextualMemory(sessionId, currentQuery)

            // Get any misconceptions that need addressing
            const misconceptions = await this.getMisconceptions(sessionId)

            // Build focused educational context
            let context = `CURRENT CONTENT:\n${chunkContent}\n\n`

            if (relevantMemories.length > 0) {
                context += `RELEVANT LEARNING CONTEXT:\n`
                relevantMemories.forEach((memory, index) => {
                    context += `${index + 1}. ${memory.memory}\n`
                })
                context += `\n`
            }

            if (misconceptions.length > 0) {
                context += `MISCONCEPTIONS TO ADDRESS:\n`
                misconceptions.forEach((misc, index) => {
                    context += `${index + 1}. ${misc.memory}\n`
                })
                context += `\n`
            }

            context += `STUDENT QUESTION: ${currentQuery}\n`

            return context
        } catch (error) {
            console.error('❌ Failed to build educational context:', error)
            // Fallback to basic context
            return `CURRENT CONTENT:\n${chunkContent}\n\nSTUDENT QUESTION: ${currentQuery}\n`
        }
    }

    async getAllMemories(documentId: string, userId: string): Promise<any[]> {
        await this.initialize()

        return await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')

                console.log(`🔍 Mem0 getAllMemories called for document: ${documentId}, user: ${userId}`)

                // Get document-specific memories using documentId as run_id
                let result: any[] = []

                try {
                    result = await this.client.getAll({
                        user_id: userId,
                        run_id: documentId
                    }) || []
                    console.log(`🔍 Document-specific memories found: ${result.length}`)
                } catch (error) {
                    console.warn('Document memory retrieval failed:', error)
                    result = []
                }

                // Filter for conversation messages and sort by timestamp
                const conversationMemories = result
                    .filter(memory =>
                        memory.metadata?.category === 'conversation' ||
                        memory.metadata?.type === 'chat_message'
                    )
                    .sort((a, b) =>
                        new Date(a.metadata?.timestamp || a.created_at).getTime() -
                        new Date(b.metadata?.timestamp || b.created_at).getTime()
                    )

                console.log(`🔍 Filtered conversation memories: ${conversationMemories.length}`)
                if (conversationMemories.length > 0) {
                    console.log('🔍 First memory sample:', JSON.stringify(conversationMemories[0], null, 2))
                }

                return conversationMemories
            },
            [],
            'document memories retrieval'
        )
    }

    async deleteSession(sessionId: string): Promise<void> {
        await this.initialize()

        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                await this.client.deleteAll({ user_id: sessionId })
                console.log(`✅ Deleted Mem0 session: ${sessionId}`)
                return true
            },
            false,
            'session deletion'
        )
    }

    async deleteAllMemories(sessionId: string): Promise<void> {
        await this.initialize()

        // Extract user ID from session ID (format: userId-documentId-timestamp)
        const userId = sessionId.split('-')[0]

        await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                
                console.log(`🧹 Deleting all memories for session: ${sessionId} (user: ${userId})`)
                
                // Delete by user_id to ensure all memories are removed
                await this.client.deleteAll({ user_id: userId })
                
                // Also try deleting by session ID as user_id (fallback)
                try {
                    await this.client.deleteAll({ user_id: sessionId })
                } catch (error) {
                    console.warn('Fallback deletion failed:', error)
                }
                
                console.log(`✅ Successfully deleted all memories for session: ${sessionId}`)
                return true
            },
            false,
            'all memories deletion'
        )
    }

    async sessionExists(sessionId: string): Promise<boolean> {
        await this.initialize()

        return await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')
                const memories = await this.client.getAll({ user_id: sessionId })
                return memories && memories.length > 0
            },
            false,
            'session existence check'
        )
    }

    async getSessionInfo(userId: string, documentId: string): Promise<{ sessionId: string | null, exists: boolean }> {
        try {
            // Get session ID from Progress table
            const progress = await dbService.progress.findUnique({
                where: {
                    userId_documentId: {
                        userId,
                        documentId
                    }
                }
            })

            if (!progress?.sessionId) {
                return { sessionId: null, exists: false }
            }

            // Check if session exists in Mem0
            const exists = await this.sessionExists(progress.sessionId)
            return { sessionId: progress.sessionId, exists }
        } catch (error) {
            console.error('❌ Failed to get session info:', error)
            return { sessionId: null, exists: false }
        }
    }

    async cleanupDocumentMemories(documentId: string): Promise<void> {
        try {
            // Since dbService.progress doesn't have findMany, we'll need to handle this differently
            // For now, we'll clean up based on the session pattern
            console.log(`⚠️ Document cleanup requested for: ${documentId}`)
            // This would need to be implemented when we have proper database queries
            // or we could store document-session mappings separately
        } catch (error) {
            console.error('❌ Failed to cleanup document memories:', error)
        }
    }

    async exportConversationData(documentId: string, userId: string): Promise<any> {
        await this.initialize()

        try {
            const memories = await this.getAllMemories(documentId, userId)

            return {
                conversations: memories.filter((m: any) => m.metadata?.category === 'conversation'),
                learningProgress: memories.filter((m: any) => m.metadata?.type === 'learning_progress'),
                concepts: memories.filter((m: any) => m.metadata?.category === 'concept_understanding'),
                misconceptions: memories.filter((m: any) => m.metadata?.category === 'misconception'),
                sessionMetadata: {
                    documentId,
                    userId,
                    totalMemories: memories.length,
                    exportedAt: new Date().toISOString()
                }
            }
        } catch (error) {
            console.error('❌ Failed to export conversation data:', error)
            return null
        }
    }

    async getSessionStatistics(documentId: string, userId: string): Promise<any> {
        await this.initialize()

        try {
            const memories = await this.getAllMemories(documentId, userId)

            const conversations = memories.filter((m: any) => m.metadata?.category === 'conversation')
            const learningProgress = memories.filter((m: any) => m.metadata?.type === 'learning_progress')
            const concepts = memories.filter((m: any) => m.metadata?.category === 'concept_understanding')
            const misconceptions = memories.filter((m: any) => m.metadata?.category === 'misconception')

            return {
                totalMemories: memories.length,
                conversationCount: conversations.length,
                learningProgressCount: learningProgress.length,
                conceptsCount: concepts.length,
                misconceptionsCount: misconceptions.length,
                sessionStarted: memories[0]?.created_at,
                lastActivity: memories[memories.length - 1]?.created_at,
                userMessages: conversations.filter((m: any) => m.metadata?.role === 'user').length,
                assistantMessages: conversations.filter((m: any) => m.metadata?.role === 'assistant').length
            }
        } catch (error) {
            console.error('❌ Failed to get session statistics:', error)
            return null
        }
    }

    async deleteDocumentMemories(documentId: string, userId: string): Promise<boolean> {
        await this.initialize()

        return await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')

                console.log(`🗑️ Deleting all memories for document: ${documentId}, user: ${userId}`)

                // Get all memories for this document
                const memories = await this.getAllMemories(documentId, userId)

                if (memories.length === 0) {
                    console.log('📭 No memories found to delete')
                    return true
                }

                // Delete each memory
                let deletedCount = 0
                for (const memory of memories) {
                    try {
                        await this.client.delete(memory.id)
                        deletedCount++
                    } catch (error) {
                        console.warn(`Failed to delete memory ${memory.id}:`, error)
                    }
                }

                console.log(`✅ Deleted ${deletedCount}/${memories.length} memories for document ${documentId}`)
                return deletedCount === memories.length
            },
            false,
            'document memory cleanup'
        )
    }

    async deleteDocumentMemories(documentId: string, userId: string): Promise<boolean> {
        await this.initialize()

        return await this.executeMemoryOperation(
            async () => {
                if (!this.client) throw new Error('Client not available')

                console.log(`🗑️ Deleting all memories for document: ${documentId}, user: ${userId}`)

                // Get all memories for this document
                const memories = await this.getAllMemories(documentId, userId)

                if (memories.length === 0) {
                    console.log('📭 No memories found to delete')
                    return true
                }

                // Delete each memory
                let deletedCount = 0
                for (const memory of memories) {
                    try {
                        await this.client.delete(memory.id)
                        deletedCount++
                    } catch (error) {
                        console.warn(`Failed to delete memory ${memory.id}:`, error)
                    }
                }

                console.log(`✅ Deleted ${deletedCount}/${memories.length} memories for document ${documentId}`)
                return deletedCount === memories.length
            },
            false,
            'document memory cleanup'
        )
    }

    async testConnection(): Promise<boolean> {
        try {
            await this.initialize()
            return true
        } catch (error) {
            return false
        }
    }
}

// Export singleton instance
export const memoryService = new MemoryService()
