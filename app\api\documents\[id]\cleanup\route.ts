import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Document Memory Cleanup API Endpoint
 * Handles cleaning up memories when documents are deleted
 */

/**
 * DELETE /api/documents/[id]/cleanup
 * Clean up all memories associated with a document
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: documentId } = await params

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document ownership
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Get session statistics before cleanup
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    let cleanupStats = null
    if (progress?.sessionId) {
      cleanupStats = await memoryService.getSessionStatistics(progress.sessionId)
    }

    // Clean up memories before deleting document
    await memoryService.cleanupDocumentMemories(documentId)

    return NextResponse.json({
      success: true,
      message: 'Document memories cleaned up successfully',
      documentId,
      documentName: document.fileName,
      cleanupStats: cleanupStats || {
        message: 'No active session found for this document'
      }
    })

  } catch (error) {
    console.error('Document cleanup error:', error)

    return NextResponse.json(
      {
        error: 'Failed to cleanup document memories',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/documents/[id]/cleanup
 * Get cleanup preview information for a document
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: documentId } = await params

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document ownership
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Get session information
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    if (!progress?.sessionId) {
      return NextResponse.json({
        hasMemories: false,
        message: 'No memories found for this document'
      })
    }

    // Get session statistics
    const stats = await memoryService.getSessionStatistics(progress.sessionId)

    return NextResponse.json({
      hasMemories: true,
      documentName: document.fileName,
      sessionId: progress.sessionId,
      statistics: stats,
      preview: {
        totalMemories: stats?.totalMemories || 0,
        conversations: stats?.conversationCount || 0,
        learningProgress: stats?.learningProgressCount || 0,
        concepts: stats?.conceptsCount || 0,
        sessionDuration: stats?.sessionStarted && stats?.lastActivity
          ? new Date(stats.lastActivity).getTime() - new Date(stats.sessionStarted).getTime()
          : 0
      }
    })

  } catch (error) {
    console.error('Document cleanup preview error:', error)

    return NextResponse.json(
      {
        error: 'Failed to get cleanup preview',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}