"use client"

import { useState, useEffect, useRef } from "react"
import { CompactUpload } from "@/components/dashboard/compact-upload"
import { useSidebar } from "@/components/ui/sidebar"
import { useSidebarContext } from "@/components/sidebar-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, ArrowUp, PanelLeft } from "lucide-react"
import { AIThinking } from "@/components/ui/ai-thinking"
import { useDocumentPersistence } from "@/hooks/use-document-persistence"
import { sessionStateManager } from "@/lib/session-state"
import { toast } from "sonner"
import { ConnectionStatus } from "@/components/ui/connection-status"
import { OfflineBanner } from "@/components/ui/offline-banner"

interface Message {
  [x: string]: any
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: string
  isThinking?: boolean
}

export default function DashboardPage() {
  const { activeDocument, setActiveDocument, isLoading: documentLoading } = useDocumentPersistence()
  const [selectedDocument, setSelectedDocument] = useState<{ id: string; name: string } | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [loading, setLoading] = useState(false)
  const [sessionLoading, setSessionLoading] = useState(false)
  const [currentChunk, setCurrentChunk] = useState(0)
  const [totalChunks, setTotalChunks] = useState(0)
  const { setOpenMobile } = useSidebar()
  const { sidebarVisible, setSidebarVisible } = useSidebarContext()
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // This effect listens for document selection changes from the sidebar
  useEffect(() => {
    const handleDocumentSelected = async (event: Event) => {
      const customEvent = event as CustomEvent<{ id: string; name: string }>
      const documentInfo = customEvent.detail

      setSelectedDocument(documentInfo)
      setOpenMobile(false) // Close sidebar on mobile after selection

      if (documentInfo) {
        // Update persistent state
        await setActiveDocument(documentInfo)
        initializeChat(documentInfo.id)
      }
    }

    const handleNavigateToUpload = async () => {
      setSelectedDocument(null)
      setMessages([])
      await setActiveDocument(null)
      setOpenMobile(false)
    }

    const handleDocumentDeleted = async (event: Event) => {
      const customEvent = event as CustomEvent<{ documentId: string }>
      const deletedDocumentId = customEvent.detail.documentId

      // Clear session state for deleted document
      sessionStateManager.clearSession(deletedDocumentId)

      // If the currently selected document was deleted, clear the selection and redirect to upload
      if (selectedDocument && selectedDocument.id === deletedDocumentId) {
        console.log('🗑️ Currently active document was deleted, redirecting to upload interface')
        
        setSelectedDocument(null)
        setMessages([])
        setCurrentChunk(0)
        setTotalChunks(0)
        await setActiveDocument(null)
        
        // Show feedback to user
        toast.info('Document deleted. Upload a new document to continue learning.')
      }

      // Document list will refresh automatically via the documentDeleted event
    }

    window.addEventListener("documentSelected", handleDocumentSelected as EventListener)
    window.addEventListener("navigateToUpload", handleNavigateToUpload as EventListener)
    window.addEventListener("documentDeleted", handleDocumentDeleted as EventListener)

    return () => {
      window.removeEventListener("documentSelected", handleDocumentSelected as EventListener)
      window.removeEventListener("navigateToUpload", handleNavigateToUpload as EventListener)
      window.removeEventListener("documentDeleted", handleDocumentDeleted as EventListener)
    }
  }, [setOpenMobile])

  // Sync with persistent document state
  useEffect(() => {
    if (!documentLoading && activeDocument) {
      console.log('🔄 Restoring active document:', activeDocument.name)
      setSelectedDocument(activeDocument)
      initializeChat(activeDocument.id)
    }
  }, [activeDocument, documentLoading])

  // Auto-resize textarea when input changes
  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }
  }, [input])

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesContainerRef.current) {
        const container = messagesContainerRef.current
        container.scrollTop = container.scrollHeight
      }
    }

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(scrollToBottom, 50)
    return () => clearTimeout(timeoutId)
  }, [messages])

  const initializeChat = async (documentId: string) => {
    setSessionLoading(true)
    try {
      console.log('🔄 Initializing chat for document:', documentId)

      // Check if session is already initialized to avoid unnecessary work
      if (sessionStateManager.isSessionInitialized(documentId)) {
        const sessionState = sessionStateManager.getSessionState(documentId)
        if (sessionState && sessionState.hasMessages) {
          console.log('✅ Session already initialized, skipping reinitalization')
          // Still need to restore UI state, so continue with API call
        }
      }

      // Try to initialize/restore session via the chat initialize endpoint
      const response = await fetch(`/api/chat/initialize`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ documentId }),
      })

      if (response.ok) {
        const responseText = await response.text()
        console.log('🔍 Raw API response:', responseText)

        let data
        try {
          data = JSON.parse(responseText)
        } catch (parseError) {
          console.error('❌ JSON Parse Error:', parseError)
          console.error('❌ Response text that failed to parse:', responseText)
          toast.error('Failed to parse server response')
          return
        }

        // Update session state manager
        sessionStateManager.markSessionInitialized(
          documentId,
          data.sessionId,
          data.messages && data.messages.length > 0,
          data.currentChunk || 0,
          data.totalChunks || 0
        )

        if (data.hasExistingSession && data.messages && data.messages.length > 0) {
          // Restore existing session with conversation history
          console.log(`📚 Restored existing session with ${data.messages.length} messages`)
          setMessages(data.messages)
          setCurrentChunk(data.currentChunk || 0)
          setTotalChunks(data.totalChunks || 0)
          return // Don't process first chunk again
        } else if (data.hasExistingSession) {
          // Session exists but no messages - just set up the state
          console.log('📝 Existing session found but no messages')
          setMessages([])
          setCurrentChunk(data.currentChunk || 0)
          setTotalChunks(data.totalChunks || 0)
          return // Don't process first chunk again
        } else if (data.firstMessage) {
          // NEW SESSION: Display the first chunk content immediately
          console.log('🆕 New session - displaying first chunk content')
          setCurrentChunk(data.currentChunk || 0)
          setTotalChunks(data.totalChunks || 0)

          // Display the first chunk content as a system message
          const firstMessage: Message = {
            id: data.firstMessage.id || 'first-chunk',
            role: data.firstMessage.role || 'system',
            content: data.firstMessage.content,
            timestamp: data.firstMessage.timestamp || new Date().toISOString(),
          }
          setMessages([firstMessage])
          return // First chunk is now displayed
        }
      } else {
        const errorData = await response.json()

        if (errorData.needsInitialization) {
          // AI needs to be initialized first - this is a new document
          console.log('🆕 Document needs AI initialization')

          // Try to get basic document info for first-time setup
          const docResponse = await fetch(`/api/documents/${documentId}`)
          if (docResponse.ok) {
            const docData = await docResponse.json()
            setCurrentChunk(0)
            setTotalChunks(docData.totalChunks || 0)
            setMessages([])

            // ALWAYS process first chunk for new documents - this is the key fix!
            console.log('🚀 Processing first chunk for new document')
            await processFirstChunkWithAI(documentId, null)
          }
          return
        }

        throw new Error(errorData.error || "Failed to initialize session")
      }
    } catch (error) {
      console.error("Failed to initialize session:", error)

      // Fallback: try to get basic document info and process first chunk
      try {
        const docResponse = await fetch(`/api/documents/${documentId}`)
        if (docResponse.ok) {
          const docData = await docResponse.json()
          setCurrentChunk(0)
          setTotalChunks(docData.totalChunks || 0)
          setMessages([])
          
          // Process first chunk even in fallback scenario
          console.log('🔄 Fallback: Processing first chunk')
          await processFirstChunkWithAI(documentId, null)
        }
      } catch (fallbackError) {
        console.error("Fallback failed:", fallbackError)
      }
    } finally {
      setSessionLoading(false)
    }
  }

  const processFirstChunkWithAI = async (documentId: string, chunkContent: string | null) => {
    console.log('🚀 Starting processFirstChunkWithAI for document:', documentId)
    setLoading(true)
    
    try {
      // If no chunk content provided, fetch it first
      let actualChunkContent: string = chunkContent || ""
      if (!actualChunkContent) {
        console.log('📥 Fetching first chunk content...')
        try {
          const chunkResponse = await fetch(`/api/documents/${documentId}/chunks/0`)
          if (chunkResponse.ok) {
            const chunkData = await chunkResponse.json()
            actualChunkContent = chunkData.content || "Let's start learning about this document."
            console.log('✅ Fetched chunk content:', actualChunkContent.substring(0, 100) + '...')
          } else {
            throw new Error("Failed to fetch chunk content")
          }
        } catch (error) {
          console.error("Failed to fetch chunk content:", error)
          // Use a placeholder message to start the conversation
          actualChunkContent = "Let's start learning about this document."
        }
      }

      // Ensure we always have a non-empty string
      if (!actualChunkContent.trim()) {
        actualChunkContent = "Let's start learning about this document."
      }

      console.log('📝 Adding chunk content as user message...')
      // Add the chunk content as a USER message (right side) - simulating user sending it
      const chunkMessage = {
        id: `chunk_${Date.now()}`,
        role: "user" as const,
        content: actualChunkContent,
        timestamp: new Date().toISOString()
      }

      setMessages(prev => [...prev, chunkMessage])

      console.log('🤔 Adding AI thinking indicator...')
      // Add AI thinking indicator message (left side)
      const thinkingMessage = {
        id: `thinking_${Date.now()}`,
        role: "assistant" as const,
        content: "thinking",
        timestamp: new Date().toISOString(),
        isThinking: true
      }

      setMessages(prev => [...prev, thinkingMessage])

      console.log('📡 Sending chunk to AI via streaming API...')
      // Send the chunk content to AI with proper educational prompts via streaming
      const response = await fetch("/api/chat/stream", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId,
          message: actualChunkContent, // Send the raw chunk content - the backend will add educational prompts
          chunkIndex: 0
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Stream request failed:', response.status, errorText)
        throw new Error(`Failed to process chunk with AI: ${response.status} ${errorText}`)
      }

      // Handle streaming response
      const reader = response.body?.getReader()
      if (!reader) throw new Error("No response body")

      const aiMessage = {
        id: `ai_${Date.now()}`,
        role: "assistant" as const,
        content: "",
        timestamp: new Date().toISOString()
      }

      console.log('🔄 Replacing thinking message with AI response...')
      // Replace thinking message with actual AI message
      setMessages(prev =>
        prev.map(msg =>
          msg.isThinking ? aiMessage : msg
        )
      )

      // Read the streaming response
      console.log('📡 Starting to read streaming response...')

      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          console.log('✅ Streaming completed successfully')
          break
        }

        const chunk = new TextDecoder().decode(value)
        console.log('📦 Received chunk:', chunk.substring(0, 100) + (chunk.length > 100 ? '...' : ''))

        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              console.log('📨 Parsed data:', data.type, data.content?.substring(0, 50))

              if (data.type === 'token' && data.content) {
                setMessages(prev =>
                  prev.map(msg =>
                    msg.id === aiMessage.id
                      ? { ...msg, content: msg.content + data.content }
                      : msg
                  )
                )
              } else if (data.type === 'provider') {
                console.log('🤖 Provider info:', data.content)
              } else if (data.type === 'status') {
                console.log('📊 Status:', data.content)
              } else if (data.type === 'error') {
                console.error('❌ Stream error:', data.content)
                throw new Error(data.content)
              }
            } catch (e) {
              console.warn('⚠️ Streaming parse error:', e, 'Line:', line)
            }
          }
        }
      }

      // Update session state to indicate we now have messages
      if (selectedDocument) {
        sessionStateManager.updateMessageCount(selectedDocument.id, true)
        console.log('✅ Updated session state - document now has messages')
      }

    } catch (error) {
      console.error("❌ Failed to process first chunk:", error)

      // Remove thinking message and add error message
      setMessages(prev => prev.filter(msg => !msg.isThinking))

      // Add error message
      const errorMessage = {
        id: `error_${Date.now()}`,
        role: "assistant" as const,
        content: "I'm having trouble processing this content right now. Please try asking me a question about it.",
        timestamp: new Date().toISOString()
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setLoading(false)
      console.log('🏁 processFirstChunkWithAI completed')
    }
  }

  const sendMessage = async () => {
    if (!input.trim() || loading || !selectedDocument) return

    const userMessage = input.trim()
    setInput("")
    setLoading(true)

    // Scroll to bottom immediately when user sends message
    setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)

    // Add user message immediately
    const newUserMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: userMessage,
      timestamp: new Date().toISOString(),
    }
    setMessages((prev) => [...prev, newUserMessage])

    // Add thinking indicator
    const thinkingMessage: Message = {
      id: `thinking_${Date.now()}`,
      role: "assistant",
      content: "thinking",
      timestamp: new Date().toISOString(),
      isThinking: true
    }
    setMessages((prev) => [...prev, thinkingMessage])

    try {
      const response = await fetch("/api/chat/message", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId: selectedDocument.id,
          chunkIndex: currentChunk,
          message: userMessage,
        }),
      })

      if (!response.ok) throw new Error("Failed to send message")

      const data = await response.json()

      // Replace thinking message with AI response
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.response,
        timestamp: new Date().toISOString(),
      }
      setMessages((prev) =>
        prev.map(msg =>
          msg.isThinking ? aiMessage : msg
        )
      )

      // Update session state to indicate we have messages
      if (selectedDocument) {
        sessionStateManager.updateMessageCount(selectedDocument.id, true)
      }
    } catch (error) {
      console.error("Failed to send message:", error)
      // Remove thinking message on error
      setMessages((prev) => prev.filter(msg => !msg.isThinking))
    } finally {
      setLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const handleUploadComplete = async (documentId: string, filename: string) => {
    // Automatically select the uploaded document and start chat
    const documentInfo = { id: documentId, name: filename }
    setSelectedDocument(documentInfo)
    await setActiveDocument(documentInfo)
    initializeChat(documentId)
  }

  const handleNextChunk = async () => {
    if (!selectedDocument || currentChunk >= totalChunks - 1) return

    setLoading(true)
    try {
      console.log(`🔄 Loading next chunk from current: ${currentChunk} to ${currentChunk + 1}`)

      const response = await fetch(`/api/chat/next-chunk`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId: selectedDocument.id,
          currentChunk: currentChunk, // Pass current chunk, API will calculate next
          direction: "next"
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("Next chunk error:", errorData)

        // If there's a currentChunk mismatch, sync with server
        if (errorData.currentChunk !== undefined) {
          setCurrentChunk(errorData.currentChunk)
        }

        throw new Error(errorData.error || "Failed to load next chunk")
      }

      const data = await response.json()

      // Update chunk index (ensure it's sequential)
      if (data.currentChunk === currentChunk + 1) {
        setCurrentChunk(data.currentChunk)
      } else {
        console.warn(`Unexpected chunk progression: expected ${currentChunk + 1}, got ${data.currentChunk}`)
        setCurrentChunk(data.currentChunk) // Sync with server anyway
      }

      // Add the new chunk content as a user message (right side)
      const chunkMessage = {
        id: `chunk_${data.currentChunk}_${Date.now()}`,
        role: "user" as const,
        content: data.chunkContent,
        timestamp: new Date().toISOString()
      }

      // Add AI response as assistant message (left side)
      const aiMessage = {
        id: `ai_chunk_${data.currentChunk}_${Date.now()}`,
        role: "assistant" as const,
        content: data.aiResponse,
        timestamp: new Date().toISOString()
      }

      // Append to existing messages (don't replace)
      setMessages(prev => [...prev, chunkMessage, aiMessage])

      // Update session state
      if (selectedDocument) {
        sessionStateManager.updateCurrentChunk(selectedDocument.id, data.currentChunk)
        sessionStateManager.updateMessageCount(selectedDocument.id, true)
      }

      console.log(`✅ Successfully loaded chunk ${data.currentChunk} and added to conversation`)

      // Show success feedback
      toast.success(`Moved to section ${data.currentChunk + 1} of ${data.totalChunks}`)

    } catch (error) {
      console.error("Failed to load next chunk:", error)
      toast.error(error instanceof Error ? error.message : "Failed to load next section")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Offline Banner */}
      <OfflineBanner />
      
      {/* Connection Status Indicator */}
      <ConnectionStatus />
      
      {/* Fixed Navbar - Using unified color #F9FAFB */}
      <div className="bg-[#F9FAFB] px-3 py-1 flex-shrink-0 flex items-center min-h-[48px]">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setSidebarVisible(!sidebarVisible)}
          className="h-8 w-8 p-1 hover:bg-gray-200"
        >
          <PanelLeft className="h-4 w-4" />
        </Button>
        {selectedDocument && (
          <div className="px-6 py-4 flex items-center justify-end">
            <div className="flex items-center gap-4">
              <div className="text-xs text-gray-600 font-body">
                Section {currentChunk + 1} of {totalChunks}
              </div>
              <Button
                onClick={handleNextChunk}
                disabled={currentChunk >= totalChunks - 1 || loading}
                className="bg-primary hover:bg-primary/90 text-white px-3 py-1 text-xs font-body disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-3 h-3 animate-spin mr-1" />
                    Loading...
                  </>
                ) : currentChunk >= totalChunks - 1 ? (
                  "Completed"
                ) : (
                  `Next Section →`
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Main Content Area */}
      {selectedDocument ? (
        // Chat interface
        <div className="flex-1 flex flex-col bg-white overflow-hidden">
          {/* Messages Area - PROPERLY Scrollable */}
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto pb-32 hide-scrollbar"
            style={{
              height: 'calc(100vh - 120px)',
              scrollBehavior: 'smooth'
            }}
          >
            {sessionLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                  <p className="text-gray-600 font-body">Starting your study session...</p>
                </div>
              </div>
            ) : (
              <div className="w-full px-6 py-6">
                {messages.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-gray-600 font-body text-lg">
                      Start a conversation about this document...
                    </p>
                  </div>
                )}
                {/* Match the input box width constraint */}
                <div className="max-w-2xl mx-auto space-y-6">
                  {messages.map((message) => (
                    <div key={message.id}>
                      {message.role === "user" ? (
                        // User message - Right aligned within the container
                        <div className="flex justify-end w-full pr-4">
                          <div className="max-w-[85%] bg-gray-100 text-gray-900 rounded-2xl px-4 py-3 font-body">
                            <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                          </div>
                        </div>
                      ) : (
                        // AI message - Left aligned but with proper spacing to match input field
                        <div className="flex justify-start w-full">
                          <div className="w-full max-w-full text-gray-900 font-body pl-4">
                            {message.isThinking ? (
                              <AIThinking />
                            ) : (
                              <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  {/* Scroll anchor */}
                  <div ref={messagesEndRef} />

                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        // Upload interface
        <div className="flex-1 flex items-center justify-center bg-white">
          <div className="text-center max-w-md">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-gray-900 mb-2 font-heading">Welcome to Guided Tutor</h1>
              <p className="text-gray-600 font-body">Upload your first document to start learning</p>
            </div>
            <CompactUpload onUploadComplete={handleUploadComplete} />
          </div>
        </div>
      )}

      {/* Fixed Input Area - Only show when document is selected */}
      {selectedDocument && (
        <div
          className="fixed bottom-0 bg-white z-10"
          style={{
            left: sidebarVisible ? '260px' : '0px',
            right: '0px',
            width: sidebarVisible ? 'calc(100vw - 260px)' : '100vw',
            padding: '1.5rem',
            margin: '0',
            boxSizing: 'border-box'
          }}
        >
          <div className="max-w-2xl mx-auto">
            <div className="relative flex items-end bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 focus-within:border-gray-400">
              <textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Teach me about..."
                disabled={loading}
                rows={1}
                className="flex-1 bg-transparent border-0 px-0 py-0 font-body text-gray-900 placeholder-gray-500 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:outline-none resize-none overflow-y-auto"
                style={{
                  minHeight: '24px',
                  maxHeight: '120px',
                  lineHeight: '24px'
                }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement
                  target.style.height = 'auto'
                  target.style.height = Math.min(target.scrollHeight, 120) + 'px'
                }}
              />
              <Button
                onClick={sendMessage}
                disabled={loading || !input.trim()}
                size="icon"
                className="ml-2 bg-primary hover:bg-primary/90 text-white rounded-full h-8 w-8 flex-shrink-0"
              >
                <ArrowUp className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
