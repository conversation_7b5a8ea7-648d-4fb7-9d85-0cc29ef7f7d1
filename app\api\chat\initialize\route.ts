import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Chat Initialization API Endpoint
 * Handles chat session initialization and restoration
 */

interface InitializeChatRequest {
  documentId: string
  chunkIndex?: number
}

/**
 * POST /api/chat/initialize
 * Initialize or restore chat session for a document
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const {
      documentId,
      chunkIndex = 0
    }: InitializeChatRequest = await request.json()

    // Validate required fields
    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Verify document is ready
    if (document.status !== 'READY') {
      return NextResponse.json(
        {
          error: 'Document is not ready for chat',
          status: document.status
        },
        { status: 400 }
      )
    }

    // Validate chunk index
    if (chunkIndex < 0 || chunkIndex >= document.totalChunks) {
      return NextResponse.json(
        { error: 'Invalid chunk index' },
        { status: 400 }
      )
    }

    // Get the requested chunk
    const chunk = await dbService.chunk.findUnique({
      where: {
        documentId_chunkIndex: {
          documentId,
          chunkIndex
        }
      }
    })

    if (!chunk) {
      return NextResponse.json(
        { error: 'Chunk not found' },
        { status: 404 }
      )
    }

    // Get or create progress record
    let progress = await dbService.progress.upsert({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      },
      update: {
        currentChunk: chunkIndex,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        documentId,
        currentChunk: chunkIndex,
        sessionId: `${session.user.id}-${documentId}-${Date.now()}`
      }
    })

    // Initialize conversation history
    let conversationHistory: any[] = []

    // Try to restore conversation history from document-specific memory
    try {
      console.log(`🔍 Attempting to retrieve memories for document: ${documentId}, user: ${session.user.id}`)

      // Get conversation history from Mem0 using document-based storage
      const memories = await memoryService.getAllMemories(documentId, session.user.id)
      console.log(`🔍 Retrieved ${memories.length} conversation memories from Mem0`)

      if (memories.length > 0) {
        // Format existing conversation history
        conversationHistory = memories
          .slice(-20) // Get last 20 messages
          .map((memory: any, index: number) => ({
            id: memory.id || `msg_${index}`,
            role: memory.metadata?.role || 'assistant',
            content: memory.memory || memory.text || memory.content,
            timestamp: memory.metadata?.timestamp || memory.created_at
          }))

        console.log(`📚 Restored ${conversationHistory.length} messages from existing session`)
      }
    } catch (error) {
      console.warn('⚠️ Failed to restore conversation history:', error)
      // Continue with empty history - don't block the user
    }

    // If no conversation history found, this is a new session - create initial conversation
    if (conversationHistory.length === 0) {
      console.log('🔄 No conversation history found, creating initial conversation')

      // Import AI provider factory for generating initial response
      const { AIProviderFactory } = await import('@/lib/ai/providers')

      // First message: Display the chunk content as document content
      const chunkMessage = {
        id: 'chunk_content',
        role: 'system' as const,
        content: `📖 **Section ${chunkIndex + 1} of ${document.totalChunks}**\n\n${chunk.content}`,
        timestamp: new Date().toISOString()
      }

      // Generate AI response for the first chunk
      console.log('🤖 Generating initial AI response for first chunk...')

      const educationalPrompt = `You are an expert AI tutor using the Socratic method to guide learning. A student is starting to learn from a document titled "${document.fileName}".

The student has just been presented with the first section of the document. Your role is to:

1. Welcome them warmly to the learning session
2. Briefly explain how you'll guide them using the Socratic method
3. Ask a thoughtful opening question about the main concept or key idea in this section
4. Encourage them to think deeply rather than just recall facts

Keep your response engaging, encouraging, and focused on helping them discover insights through questioning.

Document section content:
${chunk.content}

Generate a welcoming response that starts their learning journey.`

      try {
        // Create AI provider and initialize
        const aiProvider = AIProviderFactory.createProvider()
        await aiProvider.initialize()

        // Build context for AI generation
        const context = {
          userId: session.user.id,
          documentId,
          chunkIndex,
          chunkContent: chunk.content,
          sessionId: progress.sessionId,
          currentQuery: 'Initialize learning session'
        }

        // Generate streaming response and collect full text
        let aiResponse = ''
        const responseGenerator = aiProvider.generateResponse(educationalPrompt, context)

        for await (const token of responseGenerator) {
          aiResponse += token
        }

        const aiMessage = {
          id: 'ai_intro',
          role: 'assistant' as const,
          content: aiResponse,
          timestamp: new Date().toISOString()
        }

        // Store the initial conversation in memory
        await memoryService.addConversationMessage(
          documentId,
          session.user.id,
          chunk.content,
          'user',
          chunkIndex,
          true // isInitialMessage
        )

        await memoryService.addConversationMessage(
          documentId,
          session.user.id,
          aiResponse,
          'assistant',
          chunkIndex,
          true // isInitialMessage
        )

        conversationHistory = [chunkMessage, aiMessage]
        console.log('✅ Created initial conversation with AI-generated response')

      } catch (aiError) {
        console.error('❌ Failed to generate AI response, using fallback:', aiError)

        // Fallback message if AI generation fails
        const fallbackMessage = {
          id: 'ai_fallback',
          role: 'assistant' as const,
          content: `Welcome to our learning session! I'm your AI tutor, and I'm here to help you understand "${document.fileName}".

I've just shown you the first section of the document above. Take a moment to read through it carefully.

I'll guide you through this material using the Socratic method - asking questions to help you think deeply about the concepts and discover insights on your own.

After you've read the section, what do you think is the main concept or idea being presented? I'm interested in your initial thoughts and reasoning.`,
          timestamp: new Date().toISOString()
        }

        conversationHistory = [chunkMessage, fallbackMessage]
        console.log('✅ Created initial conversation with fallback message')
      }
    }

    // Update current chunk if different (but don't reset session)
    if (progress.currentChunk !== chunkIndex) {
      await dbService.progress.update({
        where: {
          userId_documentId: {
            userId: session.user.id,
            documentId
          }
        },
        data: {
          currentChunk: chunkIndex,
          updatedAt: new Date() // Mark as recently accessed
        }
      })
    }

    // Return chat initialization data
    return NextResponse.json({
      success: true,
      sessionId,
      documentId,
      documentName: document.fileName,
      currentChunk: chunkIndex,
      chunkContent: chunk.content,
      totalChunks: document.totalChunks,
      messages: conversationHistory,
      hasExistingSession,
      progress: {
        current: chunkIndex + 1,
        total: document.totalChunks,
        percentage: Math.round(((chunkIndex + 1) / document.totalChunks) * 100)
      },
      navigation: {
        canGoNext: chunkIndex < document.totalChunks - 1,
        canGoPrevious: chunkIndex > 0
      }
    })

  } catch (error) {
    console.error('Chat initialization API error:', error)

    return NextResponse.json(
      {
        error: 'Failed to initialize chat session',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
