import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { AIProviderFactory } from '@/lib/ai'
import { BloomLevel } from '@/lib/ai/memory-service'

/**
 * Chat Message API Endpoint
 * Handles non-streaming AI chat requests
 */

interface ChatMessageRequest {
  documentId: string
  chunkIndex: number
  message: string
  provider?: 'openai' | 'anthropic'
  bloomLevel?: BloomLevel
}

/**
 * POST /api/chat/message
 * Send message and get complete AI response (non-streaming)
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { 
      documentId, 
      chunkIndex, 
      message, 
      bloomLevel 
    }: ChatMessageRequest = await request.json()

    // Validate required fields
    if (!documentId || chunkIndex === undefined || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: documentId, chunkIndex, message' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Verify document is ready
    if (document.status !== 'READY') {
      return NextResponse.json(
        { error: 'Document is not ready for chat' },
        { status: 400 }
      )
    }

    // Get the current chunk
    const chunk = await dbService.chunk.findUnique({
      where: {
        documentId_chunkIndex: {
          documentId,
          chunkIndex
        }
      }
    })

    if (!chunk) {
      return NextResponse.json(
        { error: 'Chunk not found' },
        { status: 404 }
      )
    }

    // Get user's progress to find session ID
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: user.id,
          documentId
        }
      }
    })

    if (!progress?.sessionId) {
      return NextResponse.json(
        { error: 'AI session not initialized. Please initialize AI first.' },
        { status: 400 }
      )
    }

    // Ensure sessionId is not null (TypeScript safety)
    const sessionId = progress.sessionId
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 400 }
      )
    }

    // Create AI provider
    const aiProvider = AIProviderFactory.createProvider()
    await aiProvider.initialize()

    // Build conversation context
    const context = {
      userId: user.id,
      documentId,
      chunkIndex,
      chunkContent: chunk.content,
      sessionId, // Now guaranteed to be string, not null
      currentQuery: message,
      bloomLevel
    }

    // Generate complete response
    let fullResponse = ''
    const responseGenerator = aiProvider.generateResponse(message, context)

    for await (const token of responseGenerator) {
      fullResponse += token
    }

    // Return complete response
    return NextResponse.json({
      success: true,
      response: fullResponse,
      messageId: `msg_${Date.now()}`,
      timestamp: new Date().toISOString(),
      chunkIndex,
      documentId
    })

  } catch (error) {
    console.error('Chat message API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to process chat message',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}
