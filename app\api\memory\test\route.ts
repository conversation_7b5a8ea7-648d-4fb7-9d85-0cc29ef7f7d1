import { NextRequest, NextResponse } from 'next/server'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Memory Service Test API Endpoint
 * Tests Mem0 connection and basic operations
 */

/**
 * GET /api/memory/test
 * Test memory service connection and basic operations
 */
export async function GET() {
  try {
    // Test connection
    const isConnected = await memoryService.testConnection()
    
    if (!isConnected) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to connect to Mem0 service' 
        },
        { status: 500 }
      )
    }

    // Test basic operations
    const testSessionId = `test-${Date.now()}`
    
    try {
      // Test adding a memory
      await memoryService.addConversationMessage(
        testSessionId, 
        'This is a test message', 
        'user'
      )

      // Test retrieving memories
      const memories = await memoryService.getAllMemories(testSessionId)
      
      // Clean up test data
      await memoryService.deleteSession(testSessionId)

      return NextResponse.json({
        success: true,
        message: 'Memory service is working correctly',
        testResults: {
          connection: true,
          addMessage: true,
          retrieveMemories: true,
          memoriesCount: memories.length,
          cleanup: true
        }
      })
    } catch (operationError) {
      // Clean up on error
      try {
        await memoryService.deleteSession(testSessionId)
      } catch (cleanupError) {
        console.error('Failed to cleanup test session:', cleanupError)
      }
      
      throw operationError
    }
  } catch (error) {
    console.error('Memory service test failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Memory service test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/memory/test
 * Test memory service with custom session
 */
export async function POST(request: NextRequest) {
  try {
    const { sessionId, message, role = 'user' } = await request.json()
    
    if (!sessionId || !message) {
      return NextResponse.json(
        { error: 'sessionId and message are required' },
        { status: 400 }
      )
    }

    // Add test message
    await memoryService.addConversationMessage(sessionId, message, role as 'user' | 'assistant')
    
    // Retrieve all memories for this session
    const memories = await memoryService.getAllMemories(sessionId)
    
    return NextResponse.json({
      success: true,
      message: 'Message added successfully',
      sessionId,
      memoriesCount: memories.length,
      latestMemories: memories.slice(-5) // Return last 5 memories
    })
  } catch (error) {
    console.error('Memory service POST test failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to add message to memory',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
